.container {
  width: 100%;
  height: 100%;
  padding-top: 35px;
  background-color: #F8F8F8;
}


.content {
  margin: 16px;
  padding: 16px;
  border-radius: 10px;
  background-color: var(--background-color);
}

.section {
  margin-bottom: 24px;
}

.TitleBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  line-height: 100%;
  margin-bottom: 20px;
}
.Boxtwo{
    display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  line-height: 100%;
  padding: 20px 0px;
}
.title {
  font-size: 20px;
  font-weight: 500;
  color: #000;
}

.titleTwo {
  font-size: 14px;
  font-weight: 500;
  color: #ccc;
  margin-bottom: 10px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.connectStrategy {
  font-size: 14px;
  color: #666;
  margin-left: auto;
  margin-right: 8px;
}

.inputSection {
  margin-bottom: 24px;
  padding: 0px 5px;
}

.inputLabel {
  font-size: 14px;
  color: #000;
  margin-bottom: 8px;
  font-weight: 500;
}

.inputWrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #EFEFEF;
  height: 70px;
  border-radius: 24px;
  padding: 0 16px;
  .inputtext{
    
  }
}

.input {
  flex: 1;
  border: none;
  background: transparent;
}

.help {
  margin: 16px;
  padding: 16px;
  border-radius: 10px;
  background-color: var(--background-color);

}

.buttonSection {
  padding: 0 20px;
  margin-bottom: 40px;
}

.refreshButton {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;

  :global {
    .adm-button {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;
      color: #fff;

      &:active {
        background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
      }
    }
  }
}



.helpTitle {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}