import { useEffect, useState } from 'react';
import { Button, Toast } from 'antd-mobile';
import { useHistory } from 'react-router-dom';
import styles from './index.module.scss';
import NavigatorBar from '@/components/NavBar';
import { useTheme } from '@/utils/themeDetector';
import { PreloadImage } from '@/components/Image';
import { useRequest } from 'ahooks';
import connect from '@/Resources/hubGateway/connect.png';
import { getCentralInfo } from '@/api/hubGateway';

const Connect = () => {
    const history = useHistory();
    const [loginCode, setLoginCode] = useState('536541'); // 默认6位数字
    const [ipAddress, setIpAddress] = useState('');
    const { isDarkMode } = useTheme();

    const handleRefreshLoginCode = () => {
        getCentral()
        Toast.show('登录码已刷新');
    };
    const { run: getCentral, } = useRequest(
        getCentralInfo,
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0 && res.data) {
                    setIpAddress(res.data.url);
                    setLoginCode(res.data.passcode);
                } else {
                    Toast.show({
                        content: res?.result,
                        position: 'bottom',
                        duration: 1500,
                    })
                }
            },
            onError: () => {
                Toast.show({
                    content: '网络异常，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
            },
        }
    );
    useEffect(() => {
        getCentral();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    return (
        <div className={styles.container}>
            <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} onBack={history.goBack} />

            <div className={styles.content}>
                {/* 第一步 */}
                <div className={styles.step}>
                    <h2 className={styles.stepTitle}>第一步</h2>
                    <p className={styles.stepDescription}>
                        将电脑或平板与带中枢功能的设备连接至同一网络
                    </p>

                    <div >
                        <PreloadImage src={connect} alt="空状态图标" />
                    </div>
                </div>

                {/* 第二步 */}
                <div className={styles.step}>
                    <h2 className={styles.stepTitle}>第二步</h2>
                    <p className={styles.stepDescription}>
                        打开浏览器，在浏览器中输入中枢网关的IP地址并访问
                    </p>

                    <div className={styles.ipDisplay}>
                        {ipAddress}
                    </div>
                </div>

                {/* 第三步 */}
                <div className={styles.step}>
                    <h2 className={styles.stepTitle}>第三步</h2>
                    <p className={styles.stepDescription}>
                        把六位数字填入网页中，并点击进入
                    </p>

                    <div className={styles.codeDisplay}>
                        {loginCode.split('').map((digit, index) => (
                            <span key={index} className={styles.digit}>
                                {digit}
                            </span>
                        ))}
                    </div>
                </div>

                {/* 重新获取登录码按钮 */}
                <div className={styles.buttonSection}>
                    <Button
                        color="primary"
                        size="large"
                        onClick={handleRefreshLoginCode}
                        className={styles.refreshButton}
                    >
                        重新获取登录码
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default Connect;