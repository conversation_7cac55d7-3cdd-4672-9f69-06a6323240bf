import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import Player, { Events, IPlayerOptions } from "xgplayer";
// import { Sniffer } from "xgplayer";
import 'xgplayer/dist/index.min.css';
import "./MonitorPlayer.css";
import HlsPlugin from 'xgplayer-hls';
// import Mp4Plugin from "xgplayer-mp4";
import PlayerControl from "../plugin/ControlPlugin/PlayerControl";
import ChangeMoviePlugin from "../plugin/ChangeMoviePlugin";
import DevicePlugin, { IDevicePlugin } from "../plugin/DevicePlugin";
import { px2rem } from "@/utils/setRootFontSize";
import { PreloadImage } from "@/components/Image";
import close from "@/Resources/icon/close_white.png";
import share from '@/Resources/icon/share_white.png';
import del from '@/Resources/icon/delete_white.png';
import download from '@/Resources/icon/download_white.png';
// import detail from '@/Resources/icon/detail_dark.png';
import player_offline from '@/Resources/player/player_offline.png';
import DualPlugin from "../plugin/DualPlugin";
import start_icon from '@/Resources/player/play.png';
import { IEventBlock } from "../TimeAxis/TimeAxis";
import { ICameraDetail, IEventVideo } from "@/pages/IPC/IPC_APP/CameraDetail";
import { eventDefinition } from "../../constants";
// import List, { IListData, modalShow } from "@/components/List";
import { getDeviceType, getSystemType, getWebDavInfo } from "@/utils/DeviceType";
import { Toast } from "@/components/Toast/manager";
import { callTaskCenter, downloadAndSaveWithPlayer, downloadAndShareWithPlayer } from "@/api/cameraPlayer";
// import { move2trashbin } from "@/api/fatWall";
import { delRecordVideo, getResourcesFromWebDav } from "@/api/ipc";

export interface IMonitorPlayerOptions {
  type: 'normal' | 'modal',
  title?: string
  onClose?: () => void
  customOperations?: Array<{
    label: string;
    name: string;
    icon: string;
    navBarIcon?: string;
    color?: string;
    onClick: () => void;
  }>;
}

export interface IDualOptions {
  urls: {
    main: string
    secondary: string
    third?: string
  }
  poster: {
    main: string
    secondary: string
    third?: string
  }
  psm: {
    main: boolean,
    secondary: boolean,
    third?: boolean
  }
  cameraRef_secondary?: React.MutableRefObject<Player | null>
  cameraRef_third?: React.MutableRefObject<Player | null> // 三摄
}

interface IMonitorPlayer {
  baseConfig: {
    width?: string;
    url: string,
    type: PlayerType,
    mediaName: string
  }
  cameraRef?: React.MutableRefObject<Player | null>
  playerType?: 'normal' | 'multiple'
  urls?: string[]
  movieTitle?: string
  style?: React.CSSProperties;
  className?: string
  isDashboard?: boolean
  deviceOptions?: IDevicePlugin
  // onClick?: () => void
  videoContainerStyle?: React.CSSProperties | any
  options?: IMonitorPlayerOptions
  dualOptions?: IDualOptions
  dashboardDual?: boolean // 非全屏状态下是否悬浮静态帧
  poster?: string
  setIsPause?: (v: any) => void
  isPause?: boolean
  initPlayStatus?: boolean
  isOnline?: boolean
  eventData?: IEventVideo[];
  cameraDetail?: ICameraDetail;
  setSelectedDate?: (v: Date) => void;
  selectedDate?: Date;
  okCallback?: () => void; // 点击播放时重新获取直播拉流数据
}

export interface IIconBtn {
  onclick: () => void,
  icon: string,
  label: string,
  type: string,
  components?: React.ReactNode
}

export type PlayerType = 'Movie' | 'Live';

// 截取url
export const splitURL = (url: string) => {
  if (!url) return '';
  const webDAVParams = getWebDavInfo();
  const hostWithoutPort = window.location.host.replace(/:\d+$/, '');
  const path = `https://${hostWithoutPort}:${webDAVParams.port}${url.replace(webDAVParams.alias_root, '')}`;
  // const path = `https://**************:${webDAVParams.port}${url.replace(webDAVParams.alias_root, '')}`;
  return path // 替换别名路径字符串
}

const MonitorPlayer = (props: IMonitorPlayer & { fullScreenCallback?: (v: any) => void }) => {
  const { baseConfig, cameraRef, playerType = 'normal', urls = [], movieTitle = "", style, className, setIsPause, isOnline, okCallback,
    isDashboard, deviceOptions, videoContainerStyle, options, dualOptions, fullScreenCallback, dashboardDual, poster, initPlayStatus = true,
    isPause, eventData, cameraDetail, setSelectedDate, selectedDate } = props;
  // 保存播放器宽度
  const [curWidth, setCurWidth] = useState<string>('');
  const initWindowWidth = useRef<number>(window.innerWidth);

  useEffect(() => {
    setCurWidth(px2rem(baseConfig.width || '392px'));
  }, [baseConfig.width])

  const getPlayerHeight = useCallback((width: string) => {
    if (/%/ig.test(width)) {
      const num = Number(width.split('%')[0]) / 100;
      const height = window.innerWidth * num * 0.5625 + "px"; // 16:9比例
      return px2rem(height);
    } else {
      const num = Number(width.split('rem')[0]);
      return num * 0.5625 + "rem"; // 16:9比例
    }
  }, []);

  const curHeight = getPlayerHeight(curWidth);
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<Player | null>(null); // 播放器容器ref,当props不传时的默认ref

  const [autoHide, setAutoHide] = useState<boolean>(false);
  const [isPlay, setIsPlay] = useState<boolean>(false);
  const [isFull, setIsFull] = useState<boolean>(false);
  const [startPlay, setStartPlay] = useState<boolean>(initPlayStatus);
  // const mainUrl = splitURL(baseConfig.url);
  const [mainUrl, setMainUrl] = useState<string>('');

  const [errorLoading, setErrorLoading] = useState<boolean>(false); // 记录播放器请求失败次数,超过一定次数则不再尝试播放

  const webDAVParams = getWebDavInfo(); // 获取webDav信息
  const deviceType = getDeviceType(); // 获取设备信息

  // 设置资源请求头
  const headersParams = useMemo(() => {
    return {
      'Authorization': `Basic ${btoa(`${webDAVParams.username}:${webDAVParams.password}`)}`,
      'Content-Type': 'application/xml',
      'Depth': '1',
    }
  }, [webDAVParams.password, webDAVParams.username])

  // 绕过webDav限制，直接获取文件
  const getMainUrl = useCallback(async () => {
    if (baseConfig.url === '') return;

    if (baseConfig.type === 'Live') {
      // 直播的话是通过拉流获取
      setMainUrl(splitURL(baseConfig.url));
      return;
    }

    try {
      const res = await getResourcesFromWebDav(baseConfig.url);
      if (res && res.code === 0) {
        const hostWithoutPort = window.location.host.replace(/:\d+$/, '');
        const url = `http://${hostWithoutPort}:${webDAVParams.port_2 || res.data.port}${res.data.uri}`;
        // const url = `http://**************:${res.data.port}${res.data.uri}`;
        setMainUrl(url);
      } else {
        setMainUrl(baseConfig.url);
        Toast.show('获取视频失败，请稍后重试', { duration: 2000 });
      }
    } catch (e) {
      setMainUrl(baseConfig.url);
      Toast.show('获取视频失败，请稍后重试', { duration: 2000 });
    }
  }, [baseConfig.type, baseConfig.url, webDAVParams.port_2])

  // 监听当前环境是否发生旋转或者size变化，重新计算播放器大小 
  useEffect(() => {
    if (deviceType) return;

    window.addEventListener('resize', () => {
      const width = baseConfig.width || '392px';
      let newWidth = '';
      if (/%/ig.test(width)) {
        const num = Number(width.split('%')[0]) / 100;
        newWidth = px2rem(window.innerWidth * num + "px");
      } else {
        const num = Number(width.split('px')[0]);
        // 计算在原本的宽度中的比例
        const percent = num / initWindowWidth.current;
        newWidth = px2rem(window.innerWidth * percent + "px");
      }
      setCurWidth(newWidth);
    })
  }, [baseConfig.width, deviceType])

  useEffect(() => {
    getMainUrl(); // 初始化获取url
  }, [getMainUrl])

  const config: IPlayerOptions = useMemo(() => {
    const os = getSystemType();
    return {
      id: baseConfig.mediaName,
      url: mainUrl,
      width: curWidth,
      height: curHeight,
      playsinline: true,
      autoplay: true,
      autoplayMuted: true,
      lang: "zh",
      fullscreen: {
        rotateFullscreen: os === 'ios' ? true : false,
        useScreenOrientation: true,
        lockOrientationType: "landscape"
      },
      mobile: {
        gestureY: true,
        gestureX: false,
      },
      videoAttributes: {
        crossOrigin: "anonymous"
      },
      replay: false,
      ignores: ['start', 'enter', 'error'],
    }
  }, [baseConfig.mediaName, curHeight, curWidth, mainUrl])

  // hls播放器配置
  const hlsConfig = useMemo(() => {
    return {
      ...config,
      isLive: true,
      plugins: [HlsPlugin],
      hls: {
        fetchOptions: {
          mode: 'cors',
          headers: {
            ...headersParams
          }
        }
      }
    }
  }, [config, headersParams])

  // mp4播放器配置
  const mp4Config = useMemo(() => {
    return {
      ...config,
      // plugins: [Mp4Plugin],
    }
  }, [config])

  const eventHandler = useCallback((cameraPlay: Player) => {
    cameraPlay.on(Events.PLAYER_FOCUS, () => {
      setAutoHide(true); // 鼠标移入时显示控制栏
    });
    cameraPlay.on(Events.PLAYER_BLUR, () => {
      setAutoHide(false);
    });
    cameraPlay.on(Events.PLAY, () => {
      setIsPlay(true); // 播放时设置为播放状态
      setIsPause && setIsPause(false);
    });
    cameraPlay.on(Events.PAUSE, () => {
      setIsPlay(false); // 暂停时设置为暂停状态
      setIsPause && setIsPause(true);
    });

    cameraPlay.on(Events.FULLSCREEN_CHANGE, (isFull) => {
      setIsFull(isFull);
      fullScreenCallback && fullScreenCallback(isFull);
      cameraPlay.muted = false;
      if (videoContainerStyle) {
        let styles = ``
        Object.keys(videoContainerStyle).forEach((key) => {
          // 将驼峰属性名转换为连字符格式（如 fontSize -> font-size）
          const cssProperty = key.replace(/([A-Z])/g, '-$1').toLowerCase();
          styles += `${cssProperty}: ${videoContainerStyle[key]}; `;
        });
        cameraPlay.root!.style = styles + `width:${curWidth}; height:${curHeight} `;
      }
    })

    cameraPlay.on(Events.ERROR, async (e) => {
      console.log('播放错误事件触发:', e)

      // 播放错误的时候重新加载视频'
      cameraPlay.exitFullscreen();
      setErrorLoading(true);
      setIsFull(false);
      cameraPlay.resetState();
    })

    // 禁用控制栏
    cameraPlay.getPlugin('controls').hide();

  }, [curHeight, curWidth, fullScreenCallback, setIsPause, videoContainerStyle])

  //常用事件
  const onEvent = useCallback(() => {
    if (cameraRef) {
      eventHandler(cameraRef.current!);
      return;
    }
    eventHandler(contentRef.current!);
  }, [cameraRef, eventHandler])

  // 初始化播放器
  const initPlayer = useCallback(() => {
    if (mainUrl === '') return;

    // if (Sniffer.isHevcSupported()) {
    //   alert('可以支持h265');
    // }

    // if (Sniffer.isSupportMP4()) {
    //   alert('可以支持mp4');
    // }

    if (baseConfig.type === 'Movie' && startPlay) {
      // 如果是点播视频，且支持mp4格式，则使用mp4播放器播放视频
      try {
        const player: Player = new Player({ ...mp4Config }); // 非live时用mp4播放
        if (cameraRef) {
          cameraRef.current = player;
        } else {
          contentRef.current = player;
        }
        onEvent(); //监听常用事件

        return;
      } catch (e) {
        console.log('mp4初始化播放器失败:', e);
      }
    }

    if (startPlay && isOnline) {
      const os = getSystemType();

      // 如果是直播,手机类型是ios的时候使用默认播放
      if (os === 'ios') {
        try {
          const player: Player = new Player({ ...config });
          if (cameraRef) {
            cameraRef.current = player;
          } else {
            contentRef.current = player;
          }
          onEvent(); //监听常用事件

          return;
        } catch (e) {
          console.log('默认初始化播放器失败:', e);
        }
      }

      // 如果是直播且支持H.265格式，则使用hls播放器播放视频
      if (HlsPlugin.isSupported()) {
        try {
          const player: Player = new Player({ ...hlsConfig });
          if (cameraRef) {
            cameraRef.current = player;
          } else {
            contentRef.current = player;
          }

          onEvent(); //监听常用事件
        } catch (e) {
          console.log('hls初始化播放器失败:', e);
        }
      }
    }
  }, [baseConfig.type, cameraRef, config, hlsConfig, isOnline, mainUrl, mp4Config, onEvent, startPlay])

  // 播放错误时重新加载视频'
  const retry = useCallback(async () => {
    initPlayer(); // 重新初始化播放器
    setErrorLoading(false); // 重置错误状态，重新加载视频'
    try {
      if (cameraRef && cameraRef.current) {
        cameraRef.current.config.url = mainUrl;
        cameraRef.current.resetState(); // 重置播放器状态
        await cameraRef.current.play(); // 重新播放视频
      } else {
        if (contentRef && contentRef.current) {
          contentRef.current.config.url = mainUrl;
          contentRef.current.resetState(); // 重置播放器状态
          await contentRef.current.play(); // 重新播放视频
        }
      }
    } catch (e) {
      console.log(`播放失败: ${e}`);
    }
  }, [cameraRef, initPlayer, mainUrl])

  // 挂载时自动执行初始化播放器
  useEffect(() => {
    initPlayer();
  }, [initPlayer])

  const OptList = useMemo(() => {
    // 如果有自定义操作按钮，使用自定义的；否则使用默认的
    if (options?.customOperations && options.customOperations.length > 0) {
      return options.customOperations;
    }
    return [
      // { label: '查看详情', name: 'detail', color: '', icon: detail },
      { label: "下载", name: 'download', color: '', icon: download },
      { label: "分享", name: 'share', color: '', icon: share },
      { label: "删除", name: 'delete', color: '', icon: del },
    ]
  }, [options?.customOperations])

  const onShare = useCallback(async () => {
    if (deviceType) {
      try {
        await navigator.clipboard.writeText(baseConfig.url);
        Toast.show('复制成功');
      } catch (e) {
        console.log('shareErr', e);
      }
      return;
    }

    await downloadAndShareWithPlayer(baseConfig.url, (res) => {
      if (res.code === 0) {
        Toast.show('分享成功');
      }
    })
  }, [baseConfig.url, deviceType])

  const onDelete = useCallback(async () => {
    const res = await delRecordVideo({ videos: [baseConfig.url] }).catch((e) => {
      Toast.show('删除失败，请稍后再试');
    });
    if (res && res.code === 0) {
      Toast.show('正在删除');
      return;
    }

    if (res && res.code === 5000) {
      Toast.show('请求超时，请稍后再试');
    }
  }, [baseConfig.url])

  const onDownload = useCallback(async () => {
    await downloadAndSaveWithPlayer([baseConfig.url], (res) => {
      if (res.code === 0) {
        Toast.show('下载成功');
      }
    })
    if (deviceType) {
      await callTaskCenter();
    }
  }, [baseConfig.url, deviceType])

  // const detailList: IListData[] = useMemo(() => {
  //   return [
  //     { key: 'name', type: 'text', label: '文件名称', value: '小米C300' },
  //     { key: 'name1', type: 'text', label: '文件格式', value: 'mp4' },
  //     { key: 'nam2', type: 'text', label: '视频格式', value: 'H.265' },
  //     { key: 'name3', type: 'text', label: '大小', value: '109MB' },
  //     { key: 'name4', type: 'text', label: '位置', value: '/存储池1/监控中心' },
  //     { key: 'name5', type: 'text', label: '录制时间', value: '今天18:23' },
  //     { key: 'type', type: 'text', label: '标签', value: '' },
  //   ]
  // }, [])

  // // 视频内嵌详情按钮
  // const detailCallback = useCallback(() => {
  //   modalShow('查看详情', <div className={'player_lookBackDetail_modal_container'}>
  //     <List dataSource={detailList}></List>
  //   </div>, (m) => m.destroy(), () => null, true)
  // }, [detailList])

  // 更多按钮中的事件
  const moreEvent: { [key: string]: () => void } = useMemo(() => {
    return {
      share: onShare,
      delete: onDelete,
      download: onDownload,
      // detail: detailCallback
    }
  }, [onDelete, onDownload, onShare])

  const selectEvent = useCallback((eventName: string) => {
    // 如果是自定义操作，直接调用自定义的onClick方法
    if (options?.customOperations && options.customOperations.length > 0) {
      const customOp = options.customOperations.find(op => op.name === eventName);
      if (customOp) {
        customOp.onClick();
        return;
      }
    }

    // 否则使用默认的操作
    if (moreEvent[eventName]) {
      moreEvent[eventName]();
    }
  }, [moreEvent, options?.customOperations])

  // 关闭全屏事件
  const onClose = useCallback(() => {
    if (cameraRef && cameraRef.current) {
      cameraRef.current.exitFullscreen();
      setIsFull(false);
      return;
    }
    contentRef.current?.exitFullscreen();
    setIsFull(false);
  }, [cameraRef])

  // 时间轴事件
  const timeAxisEvents = useMemo(() => {
    const tempObj: { [key: string]: IEventBlock[] } = {}
    const camera_lens: string[] = [];
    cameraDetail?.key_frame.forEach((item) => camera_lens.push(`${cameraDetail.did}_${item.lens_id} `));
    camera_lens.forEach((item) => {
      tempObj[item] = (eventData || []).filter((it) => it.camera_lens === item).map((it) => {
        const date = new Date(Number(it.time));
        const h = date.getHours();
        const m = date.getMinutes();
        const s = date.getSeconds();
        return {
          ...it,
          camera_lens: it.camera_lens,
          event_name: it.event_name,
          day: date.getDate(),
          start: h * 3600 + m * 60 + s, end: h * 3600 + m * 60 + s + it.media_duration,
          color: it.event_name !== '' ? eventDefinition[it.event_name].color : 'rgba(229, 229, 229, 1)',
          file: it.file,
          time: it.time
        }
      })
    })
    return tempObj;
  }, [cameraDetail?.did, cameraDetail?.key_frame, eventData]);

  const baseComponent = useMemo(() => (
    <div style={{ ...style }} className={className}>
      <div ref={containerRef} className="monitorPlayer_container" style={{ ...videoContainerStyle }}>
        {
          options?.type === 'modal' ?
            <div style={{ ...videoContainerStyle }} id={baseConfig.mediaName}>
              <div className="monitorPlayer_modal_top" style={{ visibility: autoHide ? "visible" : "hidden", opacity: autoHide ? 1 : 0 }}>
                <PreloadImage onClick={() => {
                  if (cameraRef && cameraRef.current) {
                    cameraRef?.current?.exitFullscreen();
                  } else {
                    contentRef.current?.exitFullscreen();
                  }
                  setIsFull(false);
                  options.onClose && options.onClose();
                }} src={close} alt="close" />
                <div className="monitorPlayer_modal_top_title">
                  {options.title || ''}
                </div>
                <div className="monitorPlayer_modal_top_spans">
                  {
                    OptList.map((it: any) => {
                      return <div key={it.name} style={{ color: it.color }} className={`monitorPlayer_modal_top_span`} onClick={() => selectEvent(it.name)}>
                        <PreloadImage src={it?.navBarIcon || it.icon} alt='icon' />
                        {it.label}
                      </div>
                    })
                  }
                </div>
              </div>
              <PlayerControl setIsFull={setIsFull} okCallback={okCallback} cameraDetail={cameraDetail} width={baseConfig.width || '392px'} selectedDate={selectedDate}
                setSelectedDate={setSelectedDate} isPause={isPause ? true : false} dualOptions={dualOptions} isDashboard={isDashboard} isFull={isFull}
                timeAxisEvents={timeAxisEvents || {}}
                autoHide={autoHide} cameraRef={cameraRef || contentRef} type={baseConfig.type} isPlay={isPlay} setIsPlay={setIsPlay} options={options} />
            </div>
            :
            <div id={baseConfig.mediaName} style={{ ...videoContainerStyle }}>
              <div className={`monitorPlayer_top`} style={{ visibility: autoHide ? "visible" : "hidden", opacity: autoHide ? 1 : 0 }}>
                {
                  isFull ? <div className="monitorPlayer_top_close">
                    <PreloadImage className="monitorPlayer_top_close_img" onClick={onClose} src={close} alt="close" />
                  </div> : <></>
                }
                {
                  isFull ? <ChangeMoviePlugin curUrl={baseConfig.url} deviceType={deviceType ? 'pc' : 'mobile'} player={cameraRef || contentRef} movieTitle={movieTitle}
                    urls={urls} playerType={playerType} /> : <></>}
                {
                  deviceOptions ? <div className={`monitorPlayer_top_devicePlugin ${isFull ? 'full' : 'notFull'} `}>
                    <DevicePlugin {...deviceOptions} />
                  </div> : <></>}
              </div>
              <div className={`monitorPlayer_dual`}>
                {
                  // 是否配置双摄? => 是否在非全屏状态下显示静态帧? => 是否全屏 
                  dualOptions ?
                    dashboardDual ?
                      <DualPlugin isPlay={isPlay} width={baseConfig.width || '392px'} cameraRef_secondary={dualOptions.cameraRef_secondary} cameraRef_third={dualOptions.cameraRef_third}
                        type={baseConfig.type} mediaName={baseConfig.mediaName} urls={dualOptions.urls} poster={dualOptions.poster} psm={dualOptions.psm} cameraRef={cameraRef || contentRef} />
                      :
                      isFull ? <DualPlugin isPlay={isPlay} width={baseConfig.width || '392px'} cameraRef_secondary={dualOptions.cameraRef_secondary} cameraRef_third={dualOptions.cameraRef_third}
                        type={baseConfig.type} mediaName={baseConfig.mediaName} urls={dualOptions.urls} poster={dualOptions.poster} cameraRef={cameraRef || contentRef} psm={dualOptions.psm} />
                        :
                        <></>
                    : <></>
                }
              </div>

              <PlayerControl setIsFull={setIsFull} okCallback={okCallback} cameraDetail={cameraDetail} width={baseConfig.width || '392px'}
                selectedDate={selectedDate} setSelectedDate={setSelectedDate} isPause={isPause ? true : false} dualOptions={dualOptions} isDashboard={isDashboard}
                isFull={isFull} timeAxisEvents={timeAxisEvents || {}}
                autoHide={autoHide} cameraRef={cameraRef || contentRef} type={baseConfig.type} isPlay={isPlay} setIsPlay={setIsPlay} options={options} />
            </div>
        }
      </div>
    </div>
  ), [
    OptList, autoHide, baseConfig.mediaName, baseConfig.type, baseConfig.url, baseConfig.width,
    cameraDetail, cameraRef, className, dashboardDual, deviceOptions, deviceType, dualOptions, isDashboard, isFull, isPause, isPlay, movieTitle,
    okCallback, onClose, options, playerType, selectEvent, selectedDate, setSelectedDate, style, timeAxisEvents, urls, videoContainerStyle
  ])

  return (
    <div style={{ position: 'relative' }}>
      {
        startPlay ? baseConfig.type === 'Movie' ?
          baseComponent
          :
          !isOnline ? <div className="monitorPlayer_hover_container" style={{ width: curWidth, height: curHeight }}>
            <div className="monitorPlayer_hover_container_mask"></div>
            <div className="monitorPlayer_hover_container_fail">
              <PreloadImage className="monitorPlayer_hover_container_fail_img" src={player_offline} alt="offline" />
              <span>设备已离线</span>
            </div>
            {
              poster && (<PreloadImage className="monitorPlayer_hover_content_poster" src={splitURL(poster)} needHeader={true} />)
            }
          </div> :
            baseComponent
          :
          <div className="monitorPlayer_hover_container" style={{ width: curWidth, height: curHeight }}>
            <div className="monitorPlayer_hover_container_mask"></div>
            <PreloadImage onClick={() => setStartPlay(true)} className="monitorPlayer_hover_container_play" src={start_icon} />
            {
              poster && (<PreloadImage className="monitorPlayer_hover_content_poster" src={splitURL(poster)} needHeader={true} />)
            }
          </div>
      }


      {
        errorLoading && <div className="monitorPlayer_error_container" style={{ width: curWidth, height: curHeight, ...videoContainerStyle }}>
          <span>播放失败，请重试</span>
          <span style={{ color: 'red' }} onClick={retry}>重试</span>
        </div>
      }
    </div>
  )
}

export default MonitorPlayer;