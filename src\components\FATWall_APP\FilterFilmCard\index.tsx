import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import like_icon from '@/Resources/filmWall/like.png';
import { px2rem } from '@/utils/setRootFontSize';
import placeholder‌_poster from '@/Resources/filmWall/placeholder‌_col.png';

interface IFilterFilmCard {
  title: string;
  subtitle: string;
  score: number;
  cover: string;
  isLike: boolean;
  isDrama?: boolean; // 是否为电视剧
  episodes?: number; // 总集数
  onCardClick?: () => void;
}

const FilterFilmCard = (props: IFilterFilmCard) => {
  const { title, subtitle, score, cover, isDrama, episodes, onCardClick, isLike } = props;
  return (
    <div className={styles.card_container} onClick={onCardClick}>
      <div className={styles.card_content}>
        <PreloadImage src={cover || placeholder‌_poster} alt='cover' />
        <div className={styles.card_core}>
          {(() => {
            if (!score || score === 0) return '暂无评分';
            const numScore = typeof score === 'number' ? score : parseFloat(score);
            return isNaN(numScore) ? '暂无评分' : numScore.toFixed(1);
          })()}
        </div>
        {
          isLike && (
            <div className={styles.card_isLike}>
              <PreloadImage src={like_icon} alt='like' />
            </div>
          )
        }
      </div>
      <div className={styles.card_footer}>
        <span className={isDrama ? styles.card_text_drama : styles.card_text} title={title}>{title}</span>
        <div className={styles.card_info}>
          {isDrama && episodes && <span className={styles.episodes}>共{episodes}集</span>}
          {subtitle && subtitle !== '0' && <span className={isDrama ? styles.card_subtitle_drama : styles.card_subtitle} style={{ marginLeft: isDrama && episodes ? px2rem('5px') : 0 }}>{subtitle}</span>}
        </div>
      </div>
    </div>
  )
}

export default FilterFilmCard;